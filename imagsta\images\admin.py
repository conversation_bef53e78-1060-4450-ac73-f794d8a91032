from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import (
    Post, Search, Result, User, PostAnalytics, UserAnalytics,
    ContentTemplate, SavedHashtagSet, ScheduledPost,
    SocialMediaAccount, PostPublication
)


admin.site.register(User, BaseUserAdmin)


class ResultInline(admin.TabularInline):
    """Inline admin for Results."""
    model = Result
    extra = 0
    readonly_fields = ['created', 'updated']
    fields = ['image', 'dis_image', 'created']


@admin.register(Search)
class SearchAdmin(admin.ModelAdmin):
    """Enhanced Search admin with better display and filtering."""
    list_display = ['query', 'result_count', 'created', 'updated']
    list_filter = ['created', 'updated']
    search_fields = ['query']
    ordering = ['-updated']
    readonly_fields = ['created', 'updated']
    inlines = [ResultInline]

    def result_count(self, obj):
        """Display the number of results for this search."""
        return obj.results.count()
    result_count.short_description = 'Results'


@admin.register(Result)
class ResultAdmin(admin.ModelAdmin):
    """Enhanced Result admin with better display and filtering."""
    list_display = ['id', 'query', 'image_preview', 'created']
    list_filter = ['created', 'query']
    search_fields = ['query__query']
    ordering = ['-created']
    readonly_fields = ['created', 'updated', 'image_preview']

    def image_preview(self, obj):
        """Display a small preview of the image."""
        if obj.dis_image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.dis_image
            )
        return "No preview"
    image_preview.short_description = 'Preview'


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    """Enhanced Post admin with better display and filtering."""
    list_display = ['id', 'user', 'posted', 'image_preview', 'created']
    list_filter = ['posted', 'created', 'user']
    search_fields = ['user__username', 'caption', 'post_text', 'hashtags']
    ordering = ['-created']
    readonly_fields = ['created', 'updated', 'image_preview']

    fieldsets = (
        ('Basic Info', {
            'fields': ('user', 'posted')
        }),
        ('Content', {
            'fields': ('image', 'image_preview', 'caption', 'post_text', 'hashtags')
        }),
        ('Instagram Info', {
            'fields': ('published_image_id',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created', 'updated'),
            'classes': ('collapse',)
        }),
    )

    def image_preview(self, obj):
        """Display a small preview of the post image."""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 150px; max-height: 150px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = 'Preview'

    actions = ['mark_as_posted', 'mark_as_draft']

    def mark_as_posted(self, request, queryset):
        """Mark selected posts as posted."""
        updated = queryset.update(posted=True)
        self.message_user(request, f'{updated} posts marked as posted.')
    mark_as_posted.short_description = 'Mark selected posts as posted'

    def mark_as_draft(self, request, queryset):
        """Mark selected posts as draft."""
        updated = queryset.update(posted=False)
        self.message_user(request, f'{updated} posts marked as draft.')
    mark_as_draft.short_description = 'Mark selected posts as draft'


@admin.register(PostAnalytics)
class PostAnalyticsAdmin(admin.ModelAdmin):
    """Admin for Post Analytics."""
    list_display = ['post', 'engagement_rate', 'total_engagement', 'reach', 'impressions', 'last_updated']
    list_filter = ['last_updated', 'engagement_rate']
    search_fields = ['post__caption', 'post__user__username']
    readonly_fields = ['created', 'last_updated', 'total_engagement']
    ordering = ['-engagement_rate']

    def total_engagement(self, obj):
        return obj.total_engagement
    total_engagement.short_description = "Total Engagement"


@admin.register(UserAnalytics)
class UserAnalyticsAdmin(admin.ModelAdmin):
    """Admin for User Analytics."""
    list_display = ['user', 'total_posts', 'avg_engagement_rate', 'total_followers', 'created']
    list_filter = ['created', 'avg_engagement_rate']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['created']
    ordering = ['-avg_engagement_rate']


@admin.register(ContentTemplate)
class ContentTemplateAdmin(admin.ModelAdmin):
    """Admin for Content Templates."""
    list_display = ['name', 'template_type', 'industry', 'tone', 'usage_count', 'is_public', 'created']
    list_filter = ['template_type', 'industry', 'tone', 'is_public']
    search_fields = ['name', 'content', 'user__username']
    readonly_fields = ['created', 'updated', 'usage_count']
    ordering = ['-usage_count', '-created']


@admin.register(SavedHashtagSet)
class SavedHashtagSetAdmin(admin.ModelAdmin):
    """Admin for Saved Hashtag Sets."""
    list_display = ['name', 'user', 'industry', 'usage_count', 'created']
    list_filter = ['industry', 'created']
    search_fields = ['name', 'hashtags', 'user__username']
    readonly_fields = ['created', 'updated']
    ordering = ['-usage_count', '-created']


@admin.register(ScheduledPost)
class ScheduledPostAdmin(admin.ModelAdmin):
    """Admin for Scheduled Posts."""
    list_display = ['post', 'user', 'scheduled_for', 'status', 'retry_count', 'created']
    list_filter = ['status', 'scheduled_for', 'created']
    search_fields = ['post__caption', 'user__username']
    readonly_fields = ['created', 'updated', 'last_attempt']
    ordering = ['scheduled_for']


@admin.register(SocialMediaAccount)
class SocialMediaAccountAdmin(admin.ModelAdmin):
    """Admin for Social Media Accounts."""
    list_display = ['user', 'platform', 'username', 'status', 'follower_count', 'connected_at']
    list_filter = ['platform', 'status', 'connected_at']
    search_fields = ['user__username', 'username', 'display_name']
    readonly_fields = ['connected_at', 'last_sync', 'created', 'updated']
    ordering = ['-connected_at']

    fieldsets = (
        ('Account Info', {
            'fields': ('user', 'platform', 'username', 'display_name', 'profile_picture_url')
        }),
        ('Authentication', {
            'fields': ('access_token', 'refresh_token', 'token_expires_at'),
            'classes': ('collapse',)
        }),
        ('Platform Data', {
            'fields': ('platform_user_id', 'platform_data'),
            'classes': ('collapse',)
        }),
        ('Status & Metrics', {
            'fields': ('status', 'follower_count', 'following_count', 'posts_count')
        }),
        ('Permissions', {
            'fields': ('can_post', 'can_read')
        }),
        ('Timestamps', {
            'fields': ('connected_at', 'last_sync', 'created', 'updated'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PostPublication)
class PostPublicationAdmin(admin.ModelAdmin):
    """Admin for Post Publications."""
    list_display = ['post', 'account', 'status', 'published_at', 'likes_count', 'comments_count']
    list_filter = ['status', 'account__platform', 'published_at']
    search_fields = ['post__caption', 'account__username', 'platform_post_id']
    readonly_fields = ['published_at', 'last_sync', 'created']
    ordering = ['-published_at', '-created']


# Customize admin site header and title
admin.site.site_header = 'Imagsta Administration'
admin.site.site_title = 'Imagsta Admin'
admin.site.index_title = 'Welcome to Imagsta Administration'

