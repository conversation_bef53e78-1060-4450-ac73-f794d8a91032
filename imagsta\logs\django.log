

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 648, in content_calendar
    return render(request, 'calendar.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
    ...<5 lines>...
    )
django.template.exceptions.TemplateSyntaxError: 'calendar_tags' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
dict_extras
django_htmx
i18n
l10n
log
static
tz
widget_tweaks
ERROR 2025-07-12 12:49:15,424 basehttp 15404 17284 "GET /calendar/ HTTP/1.1" **********
ERROR 2025-07-12 12:49:22,764 log 15404 17284 Internal Server Error: /bulk-upload/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 949, in bulk_upload
    return render(request, 'bulk_upload.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 703, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '{ max_files' from '{ max_files'
ERROR 2025-07-12 12:49:22,786 basehttp 15404 17284 "GET /bulk-upload/ HTTP/1.1" **********
INFO 2025-07-12 12:51:29,735 basehttp 15404 17284 "GET / HTTP/1.1" **********
INFO 2025-07-12 12:51:30,785 basehttp 15404 17284 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-12 12:51:32,426 basehttp 15404 17284 "GET /admin HTTP/1.1" 301 0
INFO 2025-07-12 12:51:32,770 basehttp 15404 13144 "GET /admin/ HTTP/1.1" 200 7065
INFO 2025-07-12 12:51:35,743 basehttp 15404 16708 "GET /static/admin/css/responsive.css HTTP/1.1" 200 19548
INFO 2025-07-12 12:51:35,762 basehttp 15404 16708 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 1399
INFO 2025-07-12 12:51:35,779 basehttp 15404 7148 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 406
INFO 2025-07-12 12:51:35,817 basehttp 15404 13144 "GET /static/admin/css/base.css HTTP/1.1" 200 20565
INFO 2025-07-12 12:51:35,865 basehttp 15404 13144 "GET /static/admin/css/fonts.css HTTP/1.1" 200 443
INFO 2025-07-12 12:51:35,874 basehttp 15404 6408 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-07-12 12:51:35,879 basehttp 15404 12080 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-07-12 12:51:35,894 basehttp 15404 1472 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2391
INFO 2025-07-12 12:51:36,034 basehttp 15404 1472 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 334
INFO 2025-07-12 12:51:36,035 basehttp 15404 6408 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 383
INFO 2025-07-12 12:51:36,298 basehttp 15404 1472 "GET /static/admin/fonts/Roboto-Light-webfont.woff HTTP/1.1" 200 85692
INFO 2025-07-12 12:51:36,305 basehttp 15404 6408 "GET /static/admin/fonts/Roboto-Regular-webfont.woff HTTP/1.1" 200 85876
INFO 2025-07-12 12:51:36,314 basehttp 15404 13144 "GET /static/admin/fonts/Roboto-Bold-webfont.woff HTTP/1.1" 200 86184
ERROR 2025-07-12 12:51:44,567 log 15404 13144 Internal Server Error: /bulk-upload/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 949, in bulk_upload
    return render(request, 'bulk_upload.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 703, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '{ max_files' from '{ max_files'
ERROR 2025-07-12 12:51:44,593 basehttp 15404 13144 "GET /bulk-upload/ HTTP/1.1" **********
INFO 2025-07-12 12:51:47,845 basehttp 15404 13144 "GET /templates/ HTTP/1.1" 200 38648
ERROR 2025-07-12 12:51:51,010 log 15404 13144 Internal Server Error: /templates/preview/2/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 892, in template_preview
    return JsonResponse({
           ^^^^^^^^^^^^
NameError: name 'JsonResponse' is not defined. Did you mean: 'HttpResponse'?
ERROR 2025-07-12 12:51:51,031 basehttp 15404 13144 "GET /templates/preview/2/ HTTP/1.1" 500 74105
INFO 2025-07-12 12:51:53,619 basehttp 15404 13144 "GET /ai-studio/?template=true HTTP/1.1" 200 15748
INFO 2025-07-12 12:51:58,037 basehttp 15404 13144 "GET /templates/ HTTP/1.1" 200 38648
INFO 2025-07-12 12:52:05,954 basehttp 15404 13144 "GET /posts/ HTTP/1.1" 200 17622
INFO 2025-07-12 12:52:08,114 basehttp 15404 13144 "GET /analytics/ HTTP/1.1" 200 17419
INFO 2025-07-12 12:54:59,041 autoreload 15404 15372 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:55:05,471 autoreload 8416 1160 Watching for file changes with StatReloader
INFO 2025-07-12 12:55:14,599 autoreload 8416 1160 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:55:19,703 autoreload 8992 10964 Watching for file changes with StatReloader
INFO 2025-07-12 12:55:28,338 basehttp 8992 16804 "GET /templates/ HTTP/1.1" 200 38648
INFO 2025-07-12 12:55:28,917 basehttp 8992 16804 "GET /static/images/logoo.png HTTP/1.1" 304 0
INFO 2025-07-12 12:55:30,698 basehttp 8992 16804 "GET /templates/preview/2/ HTTP/1.1" 200 363
INFO 2025-07-12 12:55:36,605 basehttp 8992 16804 "GET /bulk-upload/ HTTP/1.1" 200 23254
INFO 2025-07-12 12:55:38,985 basehttp 8992 16804 "GET /calendar/ HTTP/1.1" 200 38513
INFO 2025-07-12 12:55:47,034 basehttp 8992 16804 "GET /bulk-upload/ HTTP/1.1" 200 23254
INFO 2025-07-12 12:55:54,234 basehttp 8992 16804 "GET /templates/ HTTP/1.1" 200 38648
INFO 2025-07-12 12:56:03,277 basehttp 8992 16804 "GET /ai-studio/?template=true HTTP/1.1" 200 15748
INFO 2025-07-12 12:56:15,510 basehttp 8992 16804 "GET /images HTTP/1.1" 200 17358
INFO 2025-07-12 12:56:33,223 basehttp 8992 16804 "GET /calendar/ HTTP/1.1" 200 63779
INFO 2025-07-12 12:56:45,762 basehttp 8992 16804 "GET /multi-platform/ HTTP/1.1" 200 17407
INFO 2025-07-12 12:56:49,796 basehttp 8992 16804 "GET /templates/ HTTP/1.1" 200 38648
WARNING 2025-07-12 12:56:52,585 log 8992 16804 Not Found: /templates/preview/3/
WARNING 2025-07-12 12:56:52,589 basehttp 8992 16804 "GET /templates/preview/3/ HTTP/1.1" 404 31
INFO 2025-07-12 12:56:56,359 basehttp 8992 16804 "GET /ai-studio/?template=true HTTP/1.1" 200 15748
INFO 2025-07-12 12:56:58,384 basehttp 8992 16804 "GET /templates/ HTTP/1.1" 200 38648
INFO 2025-07-12 12:57:02,114 basehttp 8992 16804 "GET /ai-studio/ HTTP/1.1" 200 15748
INFO 2025-07-12 12:57:17,475 basehttp 8992 16804 "GET / HTTP/1.1" 200 124001
INFO 2025-07-12 12:57:18,214 basehttp 8992 16804 "GET /static/images/tech.jpg HTTP/1.1" 304 0
INFO 2025-07-12 20:44:19,544 autoreload 15768 16808 Watching for file changes with StatReloader
INFO 2025-07-12 20:44:35,170 basehttp 15768 8540 "GET / HTTP/1.1" 200 145814
INFO 2025-07-12 20:44:36,385 basehttp 15768 8540 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-12 20:44:36,810 basehttp 15768 8540 "GET /static/images/logoo.png HTTP/1.1" 304 0
INFO 2025-07-12 20:44:37,062 basehttp 15768 8540 "GET /static/images/tech.jpg HTTP/1.1" 304 0
INFO 2025-07-12 20:44:45,881 basehttp 15768 8540 "GET /ai-studio/ HTTP/1.1" 200 15748
INFO 2025-07-12 20:44:51,155 basehttp 15768 8540 "GET /multi-platform/ HTTP/1.1" 200 17407
INFO 2025-07-12 20:49:05,909 basehttp 15768 8540 "GET /analytics/ HTTP/1.1" 200 17419
INFO 2025-07-12 20:49:16,005 basehttp 15768 8540 "GET / HTTP/1.1" 200 145814
INFO 2025-07-12 20:49:19,946 basehttp 15768 8540 "POST /new_feed/ HTTP/1.1" 200 9225
INFO 2025-07-12 20:49:23,691 basehttp 15768 8540 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 20:49:23,735 basehttp 15768 8540 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 20:49:23,877 basehttp 15768 8540 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 20:49:24,131 basehttp 15768 8540 "POST /search_images/ HTTP/1.1" 200 104
ERROR 2025-07-12 20:49:24,308 views 15768 8540 Unexpected error in search_for_images: no such column: images_search.search_count
INFO 2025-07-12 20:49:24,409 basehttp 15768 8540 "POST /search/ HTTP/1.1" 200 9463
ERROR 2025-07-12 21:07:34,750 views 15768 9752 Unexpected error in search_for_images: no such column: images_search.search_count
INFO 2025-07-12 21:07:34,754 basehttp 15768 9752 "POST /search/ HTTP/1.1" 200 9463
INFO 2025-07-12 21:07:46,068 basehttp 15768 9752 "GET /calendar/ HTTP/1.1" 200 63779
INFO 2025-07-12 21:07:53,819 basehttp 15768 9752 "GET /ai-studio/ HTTP/1.1" 200 15748
INFO 2025-07-12 21:08:12,959 basehttp 15768 9752 "POST /generate-ai-content/ HTTP/1.1" 200 2549
WARNING 2025-07-12 21:08:27,160 ai_services 15768 9752 OpenAI API key not configured
INFO 2025-07-12 21:08:27,174 basehttp 15768 9752 "POST /generate-ai-content/ HTTP/1.1" 200 9223
INFO 2025-07-12 21:12:20,143 autoreload 15768 16808 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 21:12:26,488 autoreload 13000 1688 Watching for file changes with StatReloader
INFO 2025-07-12 21:12:57,618 autoreload 13000 1688 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 21:13:01,896 autoreload 13736 11996 Watching for file changes with StatReloader
INFO 2025-07-12 21:14:26,273 autoreload 13736 11996 C:\Users\<USER>\Desktop\summur\imagsta\images\admin.py changed, reloading.
INFO 2025-07-12 21:14:30,553 autoreload 17868 14888 Watching for file changes with StatReloader
INFO 2025-07-12 21:15:13,143 autoreload 17868 14888 C:\Users\<USER>\Desktop\summur\imagsta\images\admin.py changed, reloading.
INFO 2025-07-12 21:15:17,420 autoreload 1968 16196 Watching for file changes with StatReloader
INFO 2025-07-12 21:15:45,115 autoreload 1968 16196 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 21:15:49,889 autoreload 12944 16636 Watching for file changes with StatReloader
INFO 2025-07-12 21:16:24,909 autoreload 12944 16636 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 21:16:29,440 autoreload 12924 17572 Watching for file changes with StatReloader
INFO 2025-07-12 21:16:52,184 autoreload 12924 17572 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 21:17:10,107 autoreload 14536 7024 Watching for file changes with StatReloader
INFO 2025-07-12 21:17:25,117 autoreload 14536 7024 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 21:17:29,529 autoreload 9988 14920 Watching for file changes with StatReloader
INFO 2025-07-12 21:18:04,343 autoreload 9988 14920 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 21:18:12,631 autoreload 4384 16920 Watching for file changes with StatReloader
INFO 2025-07-12 21:18:36,647 autoreload 4384 16920 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 21:18:40,544 autoreload 18364 16176 Watching for file changes with StatReloader
INFO 2025-07-12 21:18:53,407 autoreload 18364 16176 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 21:18:58,545 autoreload 13484 6536 Watching for file changes with StatReloader
INFO 2025-07-12 21:24:16,187 autoreload 11356 18192 Watching for file changes with StatReloader
INFO 2025-07-12 21:25:12,772 autoreload 17932 3240 Watching for file changes with StatReloader
INFO 2025-07-12 21:25:41,727 autoreload 8084 17352 Watching for file changes with StatReloader
INFO 2025-07-12 21:27:00,604 autoreload 1236 4224 Watching for file changes with StatReloader
INFO 2025-07-12 21:28:17,636 autoreload 13484 6536 C:\Users\<USER>\Desktop\summur\imagsta\images\admin.py changed, reloading.
INFO 2025-07-12 21:28:17,740 autoreload 1236 4224 C:\Users\<USER>\Desktop\summur\imagsta\images\admin.py changed, reloading.
INFO 2025-07-12 21:28:24,654 autoreload 12016 17208 Watching for file changes with StatReloader
INFO 2025-07-12 21:28:24,715 autoreload 17228 16344 Watching for file changes with StatReloader
INFO 2025-07-12 21:28:50,423 autoreload 12016 17208 C:\Users\<USER>\Desktop\summur\imagsta\images\admin.py changed, reloading.
INFO 2025-07-12 21:28:51,691 autoreload 17228 16344 C:\Users\<USER>\Desktop\summur\imagsta\images\admin.py changed, reloading.
INFO 2025-07-12 21:28:57,088 autoreload 4428 3052 Watching for file changes with StatReloader
INFO 2025-07-12 21:28:57,902 autoreload 16424 18220 Watching for file changes with StatReloader
INFO 2025-07-12 21:29:18,694 autoreload 7504 2280 Watching for file changes with StatReloader
INFO 2025-07-12 21:35:02,858 autoreload 9280 9352 Watching for file changes with StatReloader
INFO 2025-07-12 21:36:43,910 autoreload 9280 9352 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 21:36:48,929 autoreload 2416 14908 Watching for file changes with StatReloader
INFO 2025-07-12 21:37:10,053 basehttp 2416 13736 "GET / HTTP/1.1" 200 139847
INFO 2025-07-12 21:37:18,693 basehttp 2416 13736 "GET /ai-studio/ HTTP/1.1" 200 15748
ERROR 2025-07-12 21:37:30,594 ai_services 2416 13736 OpenAI API error: 401 - {
    "error": {
        "message": "Incorrect API key provided: demo-ope*********************real. You can find your API key at https://platform.openai.com/account/api-keys.",
        "type": "invalid_request_error",
        "param": null,
        "code": "invalid_api_key"
    }
}

ERROR 2025-07-12 21:37:31,142 ai_services 2416 13736 OpenAI API error for hashtags: 401
INFO 2025-07-12 21:37:31,162 basehttp 2416 13736 "POST /generate-ai-content/ HTTP/1.1" 200 9223
INFO 2025-07-12 21:37:44,039 basehttp 2416 13736 "GET /bulk-upload/ HTTP/1.1" 200 23254
INFO 2025-07-12 21:37:46,021 autoreload 2416 14908 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 21:37:50,923 autoreload 2360 13724 Watching for file changes with StatReloader
ERROR 2025-07-12 21:37:58,354 log 2360 10668 Internal Server Error: /templates/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 849, in content_templates
    _create_default_templates(request.user)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 899, in _create_default_templates
    ContentTemplate.objects.create(user=user, **template_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 656, in create
    obj = self.model(**kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\base.py", line 567, in __init__
    raise TypeError(
    ...<2 lines>...
    )
TypeError: ContentTemplate() got unexpected keyword arguments: 'hashtags'
ERROR 2025-07-12 21:37:58,384 basehttp 2360 10668 "GET /templates/ HTTP/1.1" 500 95677
WARNING 2025-07-12 21:38:00,655 log 2360 10668 Not Found: /favicon.ico
WARNING 2025-07-12 21:38:00,660 basehttp 2360 10668 "GET /favicon.ico HTTP/1.1" 404 7622
INFO 2025-07-12 21:38:05,609 basehttp 2360 10668 "GET /multi-platform/ HTTP/1.1" 200 17407
INFO 2025-07-12 21:38:05,856 basehttp 2360 10668 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-12 21:38:13,279 basehttp 2360 10668 "GET /calendar/ HTTP/1.1" 200 63779
INFO 2025-07-12 21:38:20,826 autoreload 2360 13724 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 21:38:27,853 autoreload 8184 13664 Watching for file changes with StatReloader
INFO 2025-07-12 21:38:34,930 basehttp 8184 17436 "GET /ai-studio/ HTTP/1.1" 200 15748
ERROR 2025-07-12 21:38:36,909 log 8184 17436 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_useranalytics.total_shares

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 568, in analytics_dashboard
    user_analytics, created = UserAnalytics.objects.get_or_create(user=request.user)
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 916, in get_or_create
    return self.get(**kwargs), False
           ~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_useranalytics.total_shares
ERROR 2025-07-12 21:38:36,973 basehttp 8184 17436 "GET /analytics/ HTTP/1.1" 500 159203
INFO 2025-07-12 21:38:42,428 basehttp 8184 17436 "GET /posts/ HTTP/1.1" 200 17622
INFO 2025-07-12 21:38:46,483 basehttp 8184 17436 "GET /images HTTP/1.1" 200 17358
INFO 2025-07-12 21:38:53,442 basehttp 8184 17436 "POST /new_feed/ HTTP/1.1" 200 9123
INFO 2025-07-12 21:38:56,435 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:38:56,642 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:38:56,788 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:38:57,004 autoreload 13260 17448 Watching for file changes with StatReloader
INFO 2025-07-12 21:38:57,120 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:38:57,578 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:38:58,137 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:38:58,450 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:38:58,777 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
ERROR 2025-07-12 21:38:59,613 views 8184 17436 Unexpected error in search_for_images: Result() got unexpected keyword arguments: 'image_alt', 'source'
INFO 2025-07-12 21:38:59,628 basehttp 8184 17436 "POST /search/ HTTP/1.1" 200 9412
INFO 2025-07-12 21:41:00,338 basehttp 8184 17436 "GET /static/images/logoo.png HTTP/1.1" 304 0
INFO 2025-07-12 21:41:00,416 basehttp 8184 17436 "GET /static/images/tech.jpg HTTP/1.1" 304 0
INFO 2025-07-12 21:41:03,639 basehttp 8184 17436 "POST /new_feed/ HTTP/1.1" 200 9123
INFO 2025-07-12 21:41:05,423 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:41:05,496 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 21:41:05,705 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 104
ERROR 2025-07-12 21:41:06,494 log 8184 17436 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 582, in analytics_dashboard
    thirty_days_ago = timezone.now() - timedelta(days=30)
                      ^^^^^^^^
NameError: name 'timezone' is not defined
ERROR 2025-07-12 21:41:06,539 basehttp 8184 17436 "GET /analytics/ HTTP/1.1" 500 74300
INFO 2025-07-12 21:41:12,305 basehttp 8184 17436 "POST /search_images/ HTTP/1.1" 200 103
ERROR 2025-07-12 21:41:12,592 views 8184 17436 Unexpected error in search_for_images: Result() got unexpected keyword arguments: 'image_alt', 'source'
INFO 2025-07-12 21:41:12,605 basehttp 8184 17436 "POST /search/ HTTP/1.1" 200 9412
INFO 2025-07-12 21:41:15,033 basehttp 8184 17436 "GET /templates/ HTTP/1.1" 200 32504
ERROR 2025-07-12 21:41:30,534 log 8184 17436 Internal Server Error: /templates/preview/2/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 948, in template_preview
    'variables': template.variables,
                 ^^^^^^^^^^^^^^^^^^
AttributeError: 'ContentTemplate' object has no attribute 'variables'
ERROR 2025-07-12 21:41:30,550 basehttp 8184 17436 "GET /templates/preview/2/ HTTP/1.1" 500 73912
INFO 2025-07-12 21:41:33,458 basehttp 8184 17436 "GET /ai-studio/?template=true HTTP/1.1" 200 15748
INFO 2025-07-12 21:41:38,599 basehttp 8184 17436 "GET /templates/ HTTP/1.1" 200 32504
ERROR 2025-07-12 21:42:51,103 log 8184 17436 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 569, in analytics_dashboard
    if created or user_analytics.last_calculated < timezone.now() - timedelta(hours=1):
                                                   ^^^^^^^^
NameError: name 'timezone' is not defined
ERROR 2025-07-12 21:42:51,147 basehttp 8184 17436 "GET /analytics/ HTTP/1.1" 500 73532
INFO 2025-07-12 21:43:32,316 autoreload 13260 17448 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 21:43:32,552 autoreload 8184 13664 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 21:43:42,242 autoreload 18100 15080 Watching for file changes with StatReloader
INFO 2025-07-12 21:43:42,418 autoreload 14460 17916 Watching for file changes with StatReloader
INFO 2025-07-12 21:44:00,197 autoreload 18100 15080 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 21:44:00,298 autoreload 14460 17916 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 21:44:10,269 autoreload 17088 17456 Watching for file changes with StatReloader
INFO 2025-07-12 21:44:10,472 autoreload 5232 17080 Watching for file changes with StatReloader
ERROR 2025-07-12 21:46:07,142 log 17088 352 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 569, in analytics_dashboard
    if created or user_analytics.last_calculated < timezone.now() - timedelta(hours=1):
                                                   ^^^^^^^^
NameError: name 'timezone' is not defined
ERROR 2025-07-12 21:46:07,203 basehttp 17088 352 "GET /analytics/ HTTP/1.1" 500 73532
WARNING 2025-07-12 21:46:10,134 log 17088 352 Not Found: /favicon.ico
WARNING 2025-07-12 21:46:10,136 basehttp 17088 352 "GET /favicon.ico HTTP/1.1" 404 8185
ERROR 2025-07-12 21:46:25,647 views 17088 352 Error optimizing posting schedule: name 'timezone' is not defined
ERROR 2025-07-12 21:46:25,665 log 17088 352 Internal Server Error: /optimize-schedule/
ERROR 2025-07-12 21:46:25,677 basehttp 17088 352 "GET /optimize-schedule/ HTTP/1.1" 500 48
ERROR 2025-07-12 21:46:34,629 views 17088 352 Error exporting analytics: name 'timezone' is not defined
ERROR 2025-07-12 21:46:34,632 log 17088 352 Internal Server Error: /analytics/export/
ERROR 2025-07-12 21:46:34,641 basehttp 17088 352 "GET /analytics/export/ HTTP/1.1" 500 39
INFO 2025-07-12 21:46:51,063 basehttp 17088 352 "GET /images HTTP/1.1" 200 17358
INFO 2025-07-12 21:47:13,174 basehttp 17088 352 "GET / HTTP/1.1" 200 137783
INFO 2025-07-12 21:47:29,176 basehttp 17088 352 "POST /new_feed/ HTTP/1.1" 200 9123
INFO 2025-07-12 21:47:32,296 basehttp 17088 352 "GET /templates/ HTTP/1.1" 200 32504
ERROR 2025-07-12 21:47:35,283 log 17088 352 Internal Server Error: /templates/preview/2/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 948, in template_preview
    'variables': template.variables,
                 ^^^^^^^^^^^^^^^^^^
AttributeError: 'ContentTemplate' object has no attribute 'variables'
ERROR 2025-07-12 21:47:35,303 basehttp 17088 352 "GET /templates/preview/2/ HTTP/1.1" 500 73912
INFO 2025-07-12 21:48:30,182 basehttp 17088 352 "GET /ai-studio/ HTTP/1.1" 200 21357
INFO 2025-07-12 22:03:23,828 basehttp 17088 4556 "GET /multi-platform/ HTTP/1.1" 200 17407
INFO 2025-07-12 22:03:25,120 basehttp 17088 4556 "GET /templates/ HTTP/1.1" 200 32504
ERROR 2025-07-12 22:03:31,349 log 17088 4556 Internal Server Error: /templates/preview/2/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 948, in template_preview
    'variables': template.variables,
                 ^^^^^^^^^^^^^^^^^^
AttributeError: 'ContentTemplate' object has no attribute 'variables'
ERROR 2025-07-12 22:03:31,364 basehttp 17088 4556 "GET /templates/preview/2/ HTTP/1.1" 500 73912
INFO 2025-07-13 11:19:12,071 basehttp 17088 16932 "GET / HTTP/1.1" 200 129465
INFO 2025-07-13 11:19:12,291 basehttp 17088 16932 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-13 11:19:12,338 basehttp 17088 16932 "GET /static/images/logoo.png HTTP/1.1" 304 0
INFO 2025-07-13 11:19:12,569 basehttp 17088 16932 "GET /static/images/tech.jpg HTTP/1.1" 304 0
INFO 2025-07-13 11:22:03,122 basehttp 17088 16932 "GET /calendar/ HTTP/1.1" 200 63779
ERROR 2025-07-13 11:22:09,645 log 17088 16932 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 569, in analytics_dashboard
    if created or user_analytics.last_calculated < timezone.now() - timedelta(hours=1):
                                                   ^^^^^^^^
NameError: name 'timezone' is not defined
ERROR 2025-07-13 11:22:09,658 basehttp 17088 16932 "GET /analytics/ HTTP/1.1" 500 73692
WARNING 2025-07-13 11:22:10,450 log 17088 16932 Not Found: /favicon.ico
WARNING 2025-07-13 11:22:10,452 basehttp 17088 16932 "GET /favicon.ico HTTP/1.1" 404 8185
INFO 2025-07-13 11:22:36,583 basehttp 17088 16932 "GET /templates/ HTTP/1.1" 200 32504
INFO 2025-07-13 11:22:39,997 basehttp 17088 16932 "GET /images HTTP/1.1" 200 17358
INFO 2025-07-13 11:22:42,135 basehttp 17088 16932 "GET /ai-studio/ HTTP/1.1" 200 21357
INFO 2025-07-13 11:22:43,047 basehttp 17088 16932 "GET /ai-studio/ HTTP/1.1" 200 21357
ERROR 2025-07-13 11:22:44,773 log 17088 16932 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 569, in analytics_dashboard
    if created or user_analytics.last_calculated < timezone.now() - timedelta(hours=1):
                                                   ^^^^^^^^
NameError: name 'timezone' is not defined
ERROR 2025-07-13 11:22:44,791 basehttp 17088 16932 "GET /analytics/ HTTP/1.1" 500 73693
INFO 2025-07-13 11:27:58,827 basehttp 5232 12500 - Broken pipe from ('127.0.0.1', 53723)
INFO 2025-07-13 11:27:59,312 basehttp 5232 4468 "GET / HTTP/1.1" 200 133324
INFO 2025-07-13 11:27:59,313 basehttp 5232 3588 - Broken pipe from ('127.0.0.1', 53718)
INFO 2025-07-13 11:28:12,357 basehttp 5232 4468 "GET / HTTP/1.1" 200 133324
INFO 2025-07-13 11:28:24,029 basehttp 5232 4468 "GET /templates/ HTTP/1.1" 200 32504
INFO 2025-07-13 11:30:15,346 basehttp 5232 4468 "GET / HTTP/1.1" 200 133324
INFO 2025-07-13 11:30:21,264 basehttp 5232 4468 "POST /new_feed/ HTTP/1.1" 200 9149
INFO 2025-07-13 11:30:24,412 basehttp 5232 4468 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-13 11:30:25,854 basehttp 5232 4468 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-13 11:30:26,035 basehttp 5232 4468 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-13 11:30:26,300 basehttp 5232 4468 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-13 11:30:26,497 basehttp 5232 4468 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-13 11:30:26,615 basehttp 5232 4468 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-13 11:30:26,758 basehttp 5232 4468 "POST /search_images/ HTTP/1.1" 200 103
ERROR 2025-07-13 11:30:27,333 views 5232 4468 Unexpected error in search_for_images: Result() got unexpected keyword arguments: 'image_alt', 'source'
INFO 2025-07-13 11:30:27,468 basehttp 5232 4468 "POST /search/ HTTP/1.1" 200 9425
INFO 2025-07-13 11:30:49,816 autoreload 5232 17080 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 11:31:02,912 autoreload 3476 7680 Watching for file changes with StatReloader
INFO 2025-07-13 11:31:13,953 basehttp 3476 11756 "POST /new_feed/ HTTP/1.1" 200 9149
INFO 2025-07-13 11:31:21,575 basehttp 3476 11756 "POST /new_feed/ HTTP/1.1" 200 9167
INFO 2025-07-13 11:31:31,910 autoreload 3476 7680 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-13 11:31:41,252 autoreload 9092 7844 Watching for file changes with StatReloader
INFO 2025-07-13 11:32:20,762 autoreload 9092 7844 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 11:32:35,651 autoreload 16904 11852 Watching for file changes with StatReloader
INFO 2025-07-13 11:33:39,959 autoreload 16904 11852 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-13 11:33:45,853 autoreload 17400 17980 Watching for file changes with StatReloader
INFO 2025-07-13 11:34:00,011 autoreload 17400 17980 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 11:34:05,660 autoreload 10392 17136 Watching for file changes with StatReloader
INFO 2025-07-13 11:35:04,412 autoreload 10392 17136 C:\Users\<USER>\Desktop\summur\imagsta\images\admin.py changed, reloading.
INFO 2025-07-13 11:35:08,909 autoreload 12844 7928 Watching for file changes with StatReloader
INFO 2025-07-13 11:35:25,751 autoreload 12844 7928 C:\Users\<USER>\Desktop\summur\imagsta\images\admin.py changed, reloading.
INFO 2025-07-13 11:35:31,854 autoreload 5992 7152 Watching for file changes with StatReloader
INFO 2025-07-13 11:35:44,521 basehttp 5992 17508 "GET / HTTP/1.1" 200 16141
INFO 2025-07-13 11:35:54,340 basehttp 5992 17508 "GET /connect-accounts/ HTTP/1.1" 200 26484
INFO 2025-07-13 11:36:02,324 basehttp 5992 17508 "GET /ai-studio/?template=3 HTTP/1.1" 200 21357
INFO 2025-07-13 11:36:06,752 basehttp 5992 17508 "GET /images HTTP/1.1" 200 17358
INFO 2025-07-13 11:36:15,075 basehttp 5992 17508 "GET /connect-accounts/ HTTP/1.1" 200 26484
INFO 2025-07-13 11:36:38,011 basehttp 5992 17508 "GET /connect-accounts/ HTTP/1.1" 200 26484
ERROR 2025-07-13 11:38:08,226 log 5992 17508 Internal Server Error: /templates/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 880, in content_templates
    return render(request, 'content_templates.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'export-templates' not found. 'export-templates' is not a valid view function or pattern name.
ERROR 2025-07-13 11:38:08,262 basehttp 5992 17508 "GET /templates/ HTTP/1.1" 500 183338
ERROR 2025-07-13 11:38:13,157 log 5992 17508 Internal Server Error: /templates/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 880, in content_templates
    return render(request, 'content_templates.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 471, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\urls\base.py", line 88, in reverse
    return resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\urls\resolvers.py", line 828, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'export-templates' not found. 'export-templates' is not a valid view function or pattern name.
ERROR 2025-07-13 11:38:13,179 basehttp 5992 17508 "GET /templates/ HTTP/1.1" 500 183338
INFO 2025-07-13 11:38:20,629 autoreload 5992 7152 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 11:38:29,187 autoreload 1696 8396 Watching for file changes with StatReloader
INFO 2025-07-13 11:38:34,704 basehttp 1696 9364 "GET /ai-studio/?template=1 HTTP/1.1" 200 21357
INFO 2025-07-13 11:38:40,651 autoreload 1696 8396 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-13 11:38:46,797 autoreload 14136 16188 Watching for file changes with StatReloader
INFO 2025-07-13 11:39:50,343 basehttp 14136 4260 "GET /templates/ HTTP/1.1" 200 39652
INFO 2025-07-13 11:39:57,431 basehttp 14136 4260 "GET /ai-studio/?template=true HTTP/1.1" 200 21357
INFO 2025-07-13 12:01:19,976 news_services 12444 812 Fetched 0 new articles from TechCrunch RSS
INFO 2025-07-13 12:26:21,092 autoreload 7420 17820 Watching for file changes with StatReloader
INFO 2025-07-13 12:26:29,572 basehttp 7420 7872 "GET / HTTP/1.1" 200 16141
INFO 2025-07-13 12:26:38,097 basehttp 7420 7872 "GET /ai-studio/ HTTP/1.1" 200 21357
INFO 2025-07-13 12:26:39,985 basehttp 7420 7872 "GET /posts/ HTTP/1.1" 200 17622
INFO 2025-07-13 12:26:41,931 basehttp 7420 7872 "GET /ai-studio/ HTTP/1.1" 200 21357
INFO 2025-07-13 12:26:46,927 basehttp 7420 7872 "GET /images HTTP/1.1" 200 17358
INFO 2025-07-13 12:28:23,292 autoreload 7420 17820 C:\Users\<USER>\Desktop\summur\imagsta\images\ai_services.py changed, reloading.
INFO 2025-07-13 12:28:34,911 autoreload 12448 6564 Watching for file changes with StatReloader
INFO 2025-07-13 12:29:01,730 autoreload 12448 6564 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-13 12:29:08,699 autoreload 8888 11940 Watching for file changes with StatReloader
INFO 2025-07-13 12:29:39,851 autoreload 8888 11940 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 12:29:45,984 autoreload 7080 18168 Watching for file changes with StatReloader
INFO 2025-07-13 12:30:27,304 autoreload 7080 18168 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 12:30:35,425 autoreload 7932 8452 Watching for file changes with StatReloader
INFO 2025-07-13 12:30:55,766 autoreload 7932 8452 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 12:31:01,296 autoreload 17216 14664 Watching for file changes with StatReloader
INFO 2025-07-13 12:31:10,628 autoreload 17216 14664 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 12:31:18,223 autoreload 3852 17476 Watching for file changes with StatReloader
INFO 2025-07-13 12:34:10,786 news_services 17464 6680 Fetched 0 new articles from BBC Technology RSS
INFO 2025-07-13 12:34:11,270 news_services 17464 6680 Fetched 0 new articles from ESPN RSS
INFO 2025-07-13 12:34:12,970 news_services 17464 6680 Fetched 20 new articles from Reddit Programming
INFO 2025-07-13 12:34:15,627 news_services 17464 6680 Fetched 25 new articles from Reddit Technology
INFO 2025-07-13 12:34:16,435 news_services 17464 6680 Fetched 0 new articles from Science Daily RSS
INFO 2025-07-13 12:34:16,580 news_services 17464 6680 Fetched 0 new articles from TechCrunch RSS
INFO 2025-07-13 12:35:14,955 autoreload 14600 10484 Watching for file changes with StatReloader
INFO 2025-07-13 12:36:21,199 autoreload 17652 10376 Watching for file changes with StatReloader
INFO 2025-07-13 12:36:32,746 basehttp 17652 17104 "GET / HTTP/1.1" 200 16305
INFO 2025-07-13 12:36:57,831 autoreload 17652 10376 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-13 12:37:04,646 autoreload 16236 10588 Watching for file changes with StatReloader
INFO 2025-07-13 12:37:31,049 autoreload 16236 10588 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-13 12:37:41,175 autoreload 1808 9496 Watching for file changes with StatReloader
INFO 2025-07-13 15:10:32,319 basehttp 1808 9024 "GET / HTTP/1.1" 200 16480
INFO 2025-07-13 15:10:32,683 basehttp 1808 9024 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-13 15:10:32,698 basehttp 1808 19748 "GET /static/images/tech.jpg HTTP/1.1" 304 0
INFO 2025-07-13 15:10:32,766 basehttp 1808 19748 "GET /static/images/logoo.png HTTP/1.1" 304 0
INFO 2025-07-13 15:10:38,562 basehttp 1808 19748 "GET /ai-studio/ HTTP/1.1" 200 21696
ERROR 2025-07-13 15:10:46,880 views 1808 19748 Error in news dashboard: Cannot find 'articles' on Category object, 'articles' is an invalid parameter to prefetch_related()
INFO 2025-07-13 15:10:46,887 basehttp 1808 19748 "GET /news-dashboard/ HTTP/1.1" 302 0
INFO 2025-07-13 15:10:46,909 basehttp 1808 19748 "GET / HTTP/1.1" 200 16480
ERROR 2025-07-13 15:10:52,603 views 1808 19748 Error in news dashboard: Cannot find 'articles' on Category object, 'articles' is an invalid parameter to prefetch_related()
INFO 2025-07-13 15:10:52,616 basehttp 1808 19748 "GET /news-dashboard/ HTTP/1.1" 302 0
INFO 2025-07-13 15:10:58,236 basehttp 1808 19748 "GET /analytics/ HTTP/1.1" 200 23091
INFO 2025-07-13 15:11:03,967 basehttp 1808 19748 "GET /ai-studio/ HTTP/1.1" 200 21696
INFO 2025-07-13 15:11:08,416 basehttp 1808 19748 "GET /posts/ HTTP/1.1" 200 17961
INFO 2025-07-13 15:11:12,398 basehttp 1808 19748 "GET /images HTTP/1.1" 200 17697
ERROR 2025-07-13 15:11:45,099 views 1808 19748 Error in news dashboard: Cannot find 'articles' on Category object, 'articles' is an invalid parameter to prefetch_related()
INFO 2025-07-13 15:11:45,110 basehttp 1808 19748 "GET /news-dashboard/ HTTP/1.1" 302 0
ERROR 2025-07-13 15:11:51,743 views 1808 19748 Error in news dashboard: Cannot find 'articles' on Category object, 'articles' is an invalid parameter to prefetch_related()
INFO 2025-07-13 15:11:51,746 basehttp 1808 19748 "GET /news-dashboard/ HTTP/1.1" 302 0
